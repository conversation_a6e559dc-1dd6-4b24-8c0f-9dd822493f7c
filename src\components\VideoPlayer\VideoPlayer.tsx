import React, { useEffect, useRef } from 'react';
import videojs, { VideoJsPlayer } from 'video.js';
import 'video.js/dist/video-js.css';

// 定义 VideoPlayer 组件的 props 类型
interface VideoPlayerProps {
  options: {
    sources: Array<{
      src: string;
      type: string;
    }>;
  };
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ options }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current) {
      // 初始化 Video.js 播放器
      const player: VideoJsPlayer = videojs(videoRef.current, options, () => {
        console.log('Player is ready');
      });

      // 返回清理函数
      return () => {
        if (player) {
          player.dispose();
        }
      };
    }
  }, [options]);

  return <video ref={videoRef} className="video-js vjs-default-skin" />;
};

export default VideoPlayer;